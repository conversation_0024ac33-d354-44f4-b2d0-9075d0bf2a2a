[build-system]
requires = ["setuptools>=61.0"]
build-backend = "setuptools.build_meta"

[project]
name = "mihomo"
version = "1.1.7"
authors = [
  { name="KT", email="<EMAIL>" },
]
description = "A simple Python Pydantic model for Honkai: Star Rail parsed data from the Mihomo API."
readme = "README.md"
requires-python = ">=3.10"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
  "aiohttp==3.*",
  "pydantic==1.*",
]