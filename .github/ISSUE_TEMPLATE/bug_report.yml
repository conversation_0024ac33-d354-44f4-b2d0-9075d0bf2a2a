name: Bug Report
description: "Report  Mihomo bug"
title: "[Bug] "
labels: ["bug"]
body:
  - type: checkboxes
    id: ensure
    attributes:
      label: Verify steps
      description: Before submitting, please check all the options below to confirm that you have read and understood the following requirements; otherwise, this issue will be closed.
      options:
        - label: I have read the [documentation](https://wiki.metacubex.one/) and understand the meaning of all the configuration items I have written, rather than just piling up seemingly useful options or default values.
          required: false
        - label: I have carefully reviewed the [documentation](https://wiki.metacubex.one/) and have not resolved the issue.
          required: false
        - label: I have searched the [Issue Tracker](……/) for the issue I want to raise and did not find it.
          required: false
        - label: I am a non-Chinese user.
          required: false
        - label: I have tested with the latest Alpha branch version, and the issue still persists.
          required: true
        - label: I have provided the server and client configuration files and processes that can reproduce the issue locally, rather than a sanitized complex client configuration file.
          required: true
        - label: I provided the simplest configuration that can be used to reproduce the errors in my report, rather than relying on remote servers or piling on a lot of unnecessary configurations for reproduction.
          required: true
        - label: I have provided complete logs, rather than just the parts I think are useful out of confidence in my own intelligence.
          required: true
        - label: I have directly reproduced the error using the Mihomo command-line program, rather than using other tools or scripts.
          required: true

  - type: dropdown
    attributes:
      label: Operating System
      description: "Please provide the type of operating system."
      multiple: true
      options:
        - MacOS
        - Windows
        - Linux
        - OpenBSD/FreeBSD
        - Android
  - type: input
    attributes:
      label: System Version
      description: "Please provide the version of the operating system where the issue occurred."
    validations:
      required: true
  - type: textarea
    attributes:
      label: Mihomo Version
      description: "Provide the output of the `mihomo -v` command."
    validations:
      required: true
  - type: textarea
    attributes:
      render: yaml
      label: Configuration File
      description: |-
        Please attach the Mihomo configuration file below.
        Make sure there is no sensitive information in the configuration file (such as server addresses, passwords, ports, etc.)
        Also, ensure that the configuration file can reproduce the error using the Mihomo command-line program locally (if it's a proxy protocol issue, make sure the local server can be used for reproduction).
    validations:
      required: true
  - type: textarea
    attributes:
      label: Description
      description: "Please provide a detailed description of the error."
    validations:
      required: true
  - type: textarea
    attributes:
      label: Reproduction Steps
      description: "Please provide the steps to reproduce the error."
    validations:
      required: true
  - type: textarea
    attributes:
      label: Logs
      description: "Attach the running logs of Mihomo Core below, with `log-level` set to `DEBUG`."
      render: shell